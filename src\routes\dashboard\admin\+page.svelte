<script lang="ts">
  import { onMount } from 'svelte';
  import type { PageData } from './$types';

  export let data: PageData;

  interface User {
    id: string;
    email: string;
    name: string | null;
    isVerified: boolean;
    isAdmin: boolean;
    isSuspended: boolean;
    createdAt: string;
    timezone: string | null;
  }

  interface CreateUserResponse {
    user: User;
    credentials: {
      password: string;
    };
  }

  let users: User[] = [];
  let totalCount = 0;
  let loading = false;
  let error = '';
  let success = '';
  let showCreateForm = false;
  let newUserEmail = '';
  let createdCredentials: CreateUserResponse['credentials'] | null = null;
  let resetPasswordResult: { email: string; password: string } | null = null;

  // Modal states
  let showSuspendModal = false;
  let showDeleteModal = false;
  let userToSuspend: { id: string; email: string; isSuspended: boolean } | null = null;
  let userToDelete: { id: string; email: string } | null = null;
  let suspendingUser = false;
  let deletingUser = false;

  // Search and test email states
  let searchQuery = '';
  let sendingTestEmail = false;
  let testEmailResults: { [userId: string]: { success: boolean; message: string } } = {};

  async function loadUsers() {
    try {
      loading = true;
      const response = await fetch('/api/admin/users');
      
      if (!response.ok) {
        throw new Error('Failed to load users');
      }

      const data = await response.json();
      users = data.users;
      totalCount = data.totalCount;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load users';
    } finally {
      loading = false;
    }
  }

  async function createUser() {
    if (!newUserEmail.trim()) {
      error = 'Email is required';
      return;
    }

    try {
      loading = true;
      error = '';
      
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newUserEmail.trim()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }

      const result: CreateUserResponse = await response.json();
      createdCredentials = result.credentials;
      success = 'User created successfully! Credentials have been sent via email.';

      // Reset form
      newUserEmail = '';
      showCreateForm = false;
      
      // Reload users list
      await loadUsers();
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to create user';
    } finally {
      loading = false;
    }
  }

  async function resetUserPassword(userId: string, userEmail: string) {
    try {
      loading = true;
      error = '';

      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          action: 'reset_password'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reset password');
      }

      const result = await response.json();
      resetPasswordResult = {
        email: userEmail,
        password: result.newPassword
      };
      success = 'Password reset successfully! New password has been sent via email.';

    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to reset password';
    } finally {
      loading = false;
    }
  }

  function confirmSuspendUser(userId: string, userEmail: string, currentStatus: boolean) {
    userToSuspend = { id: userId, email: userEmail, isSuspended: currentStatus };
    showSuspendModal = true;
  }

  function cancelSuspend() {
    showSuspendModal = false;
    userToSuspend = null;
  }

  async function toggleUserSuspension() {
    if (!userToSuspend) return;

    const action = userToSuspend.isSuspended ? 'unsuspend' : 'suspend';

    try {
      suspendingUser = true;
      error = '';

      const response = await fetch(`/api/admin/users/${userToSuspend.id}/suspend`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isSuspended: !userToSuspend.isSuspended
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} user`);
      }

      success = `User ${action}ed successfully!`;
      showSuspendModal = false;
      userToSuspend = null;
      await loadUsers(); // Reload users list

    } catch (err) {
      error = err instanceof Error ? err.message : `Failed to ${action} user`;
    } finally {
      suspendingUser = false;
    }
  }

  function confirmDeleteUser(userId: string, userEmail: string) {
    userToDelete = { id: userId, email: userEmail };
    showDeleteModal = true;
  }

  function cancelDelete() {
    showDeleteModal = false;
    userToDelete = null;
  }

  async function deleteUser() {
    if (!userToDelete) return;

    try {
      deletingUser = true;
      error = '';

      const response = await fetch(`/api/admin/users/${userToDelete.id}/delete`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }

      success = 'User deleted successfully!';
      showDeleteModal = false;
      userToDelete = null;
      await loadUsers(); // Reload users list

    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to delete user';
    } finally {
      deletingUser = false;
    }
  }

  async function sendTestEmail(userId: string, userEmail: string) {
    try {
      sendingTestEmail = true;
      error = '';

      const response = await fetch(`/api/admin/users/${userId}/test-email`, {
        method: 'POST'
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send test email');
      }

      testEmailResults[userId] = { success: true, message: 'Test email sent successfully!' };
      success = `Test email sent to ${userEmail}`;

      // Clear the result after 5 seconds
      setTimeout(() => {
        delete testEmailResults[userId];
        testEmailResults = { ...testEmailResults };
      }, 5000);

    } catch (err) {
      testEmailResults[userId] = {
        success: false,
        message: err instanceof Error ? err.message : 'Failed to send test email'
      };
      error = `Failed to send test email to ${userEmail}`;

      // Clear the result after 5 seconds
      setTimeout(() => {
        delete testEmailResults[userId];
        testEmailResults = { ...testEmailResults };
      }, 5000);
    } finally {
      sendingTestEmail = false;
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Filter users based on search query
  $: filteredUsers = users.filter(user => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    return (
      user.email.toLowerCase().includes(query) ||
      (user.name && user.name.toLowerCase().includes(query)) ||
      (user.isAdmin && 'admin'.includes(query)) ||
      (user.isSuspended && 'suspended'.includes(query)) ||
      (!user.isSuspended && 'active'.includes(query))
    );
  });

  async function copyToClipboard(text: string, type: string) {
    try {
      await navigator.clipboard.writeText(text);
      success = `${type} copied to clipboard!`;
      setTimeout(() => {
        success = '';
      }, 2000);
    } catch (err) {
      error = `Failed to copy ${type.toLowerCase()}`;
      setTimeout(() => {
        error = '';
      }, 2000);
    }
  }

  onMount(() => {
    loadUsers();
  });
</script>

<svelte:head>
  <title>Admin Panel - Routine Mail</title>
</svelte:head>

<div class="admin-container">
  <div class="admin-header">
    <h1>Admin Panel</h1>
    <p class="admin-subtitle">Manage users and system settings</p>
  </div>

  {#if error}
    <div class="alert alert-error">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="alert alert-success">
      {success}
    </div>
  {/if}

  {#if createdCredentials}
    <div class="credentials-display">
      <h3>User Created Successfully</h3>
      <p>Here are the generated credentials (also sent via email):</p>
      <div class="credentials-box">
        <div class="credential-item">
          <span><strong>Password:</strong> <code>{createdCredentials.password}</code></span>
          <button
            class="copy-btn"
            on:click={() => copyToClipboard(createdCredentials.password, 'Password')}
            title="Copy password"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </div>
      </div>
      <button class="btn btn-secondary" on:click={() => createdCredentials = null}>
        Close
      </button>
    </div>
  {/if}

  {#if resetPasswordResult}
    <div class="credentials-display">
      <h3>Password Reset Successfully</h3>
      <p>Here is the new password for {resetPasswordResult.email} (also sent via email):</p>
      <div class="credentials-box">
        <div class="credential-item">
          <span><strong>Email:</strong> <code>{resetPasswordResult.email}</code></span>
          <button
            class="copy-btn"
            on:click={() => copyToClipboard(resetPasswordResult.email, 'Email')}
            title="Copy email"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </div>
        <div class="credential-item">
          <span><strong>New Password:</strong> <code>{resetPasswordResult.password}</code></span>
          <button
            class="copy-btn"
            on:click={() => copyToClipboard(resetPasswordResult.password, 'Password')}
            title="Copy password"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </div>
      </div>
      <button class="btn btn-secondary" on:click={() => resetPasswordResult = null}>
        Close
      </button>
    </div>
  {/if}

  <div class="admin-stats">
    <div class="stat-card">
      <h3>Total Users</h3>
      <p class="stat-number">{totalCount}</p>
    </div>
    <div class="stat-card">
      <h3>Verified Users</h3>
      <p class="stat-number">{users.filter(u => u.isVerified).length}</p>
    </div>
    <div class="stat-card">
      <h3>Admin Users</h3>
      <p class="stat-number">{users.filter(u => u.isAdmin).length}</p>
    </div>
  </div>

  <div class="admin-actions">
    <button
      class="btn btn-primary"
      on:click={() => showCreateForm = !showCreateForm}
      disabled={loading}
    >
      {showCreateForm ? 'Cancel' : 'Create New User'}
    </button>

    <button
      class="btn btn-secondary"
      on:click={loadUsers}
      disabled={loading}
    >
      {loading ? 'Loading...' : 'Refresh'}
    </button>

    <button
      class="btn btn-scheduler"
      on:click={() => goto('/admin/scheduler')}
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      Scheduler Management
    </button>
  </div>

  {#if showCreateForm}
    <div class="create-user-form">
      <h3>Create New User</h3>
      <form on:submit|preventDefault={createUser}>
        <div class="form-group">
          <label for="email">Email *</label>
          <input
            id="email"
            type="email"
            bind:value={newUserEmail}
            placeholder="<EMAIL>"
            required
            disabled={loading}
          />
        </div>
        

        
        <div class="form-actions">
          <button type="submit" class="btn btn-primary" disabled={loading}>
            {loading ? 'Creating...' : 'Create User'}
          </button>
          <button 
            type="button" 
            class="btn btn-secondary" 
            on:click={() => showCreateForm = false}
            disabled={loading}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  {/if}

  <div class="users-table">
    <div class="users-table-header">
      <h3>Users List ({filteredUsers.length} of {totalCount})</h3>
      <div class="search-container">
        <input
          type="text"
          placeholder="Search users by email, name, or status..."
          bind:value={searchQuery}
          class="search-input"
        />
        {#if searchQuery}
          <button
            class="clear-search-btn"
            on:click={() => searchQuery = ''}
            title="Clear search"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        {/if}
      </div>
    </div>

    {#if loading && users.length === 0}
      <div class="loading">Loading users...</div>
    {:else if filteredUsers.length === 0}
      <div class="empty-state">
        {searchQuery ? 'No users match your search' : 'No users found'}
      </div>
    {:else}
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Email</th>
              <th>Name</th>
              <th>Status</th>
              <th>Account</th>
              <th>Role</th>
              <th>Created</th>
              <th>Timezone</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {#each filteredUsers as user (user.id)}
              <tr>
                <td>{user.email}</td>
                <td>{user.name || '-'}</td>
                <td>
                  <span class="status-badge" class:verified={user.isVerified}>
                    {user.isVerified ? 'Verified' : 'Unverified'}
                  </span>
                </td>
                <td>
                  <span class="account-badge" class:suspended={user.isSuspended}>
                    {user.isSuspended ? 'Suspended' : 'Active'}
                  </span>
                </td>
                <td>
                  <span class="role-badge" class:admin={user.isAdmin}>
                    {user.isAdmin ? 'Admin' : 'User'}
                  </span>
                </td>
                <td>{formatDate(user.createdAt)}</td>
                <td>{user.timezone || '-'}</td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="btn btn-reset"
                      on:click={() => resetUserPassword(user.id, user.email)}
                      disabled={loading}
                      title="Reset password for {user.email}"
                    >
                      Reset
                    </button>

                    <button
                      class="btn btn-test-email"
                      on:click={() => sendTestEmail(user.id, user.email)}
                      disabled={sendingTestEmail}
                      title="Send test email to {user.email}"
                    >
                      {sendingTestEmail ? 'Sending...' : 'Test Email'}
                    </button>

                    {#if !user.isAdmin}
                      <button
                        class="btn btn-suspend"
                        class:btn-unsuspend={user.isSuspended}
                        on:click={() => confirmSuspendUser(user.id, user.email, user.isSuspended)}
                        disabled={loading}
                        title="{user.isSuspended ? 'Unsuspend' : 'Suspend'} {user.email}"
                      >
                        {user.isSuspended ? 'Unsuspend' : 'Suspend'}
                      </button>

                      <button
                        class="btn btn-delete"
                        on:click={() => confirmDeleteUser(user.id, user.email)}
                        disabled={loading}
                        title="Delete {user.email}"
                      >
                        Delete
                      </button>
                    {/if}
                  </div>

                  {#if testEmailResults[user.id]}
                    <div class="test-email-result" class:success={testEmailResults[user.id].success} class:error={!testEmailResults[user.id].success}>
                      {testEmailResults[user.id].message}
                    </div>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>
</div>

<!-- Suspend/Unsuspend Confirmation Modal -->
{#if showSuspendModal && userToSuspend}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    on:click={cancelSuspend}
    on:keydown={(e) => e.key === 'Escape' && cancelSuspend()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="suspend-modal-title"
    tabindex="0"
  >
    <div
      class="bg-white rounded-xl shadow-large max-w-md w-full"
      on:click|stopPropagation
      on:keydown|stopPropagation
      role="document"
    >
      <div class="px-6 py-4 border-b border-slate-200">
        <div class="flex items-center justify-between">
          <h3 id="suspend-modal-title" class="text-lg font-semibold text-slate-900">
            {userToSuspend.isSuspended ? 'Unsuspend' : 'Suspend'} User
          </h3>
          <button
            class="text-slate-400 hover:text-slate-600 transition-colors"
            on:click={cancelSuspend}
            aria-label="Close suspend confirmation dialog"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <div class="px-6 py-4">
        <p class="text-slate-600">
          Are you sure you want to {userToSuspend.isSuspended ? 'unsuspend' : 'suspend'} user
          <strong>{userToSuspend.email}</strong>?
          {#if !userToSuspend.isSuspended}
            This will prevent them from logging in.
          {:else}
            This will allow them to log in again.
          {/if}
        </p>
      </div>
      <div class="px-6 py-4 border-t border-slate-200 flex gap-3 justify-end">
        <button
          class="btn btn-secondary"
          on:click={cancelSuspend}
          disabled={suspendingUser}
        >
          Cancel
        </button>
        <button
          class="btn {userToSuspend.isSuspended ? 'btn-primary' : 'btn-suspend'}"
          on:click={toggleUserSuspension}
          disabled={suspendingUser}
        >
          {#if suspendingUser}
            {userToSuspend.isSuspended ? 'Unsuspending...' : 'Suspending...'}
          {:else}
            {userToSuspend.isSuspended ? 'Unsuspend User' : 'Suspend User'}
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Delete Confirmation Modal -->
{#if showDeleteModal && userToDelete}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    on:click={cancelDelete}
    on:keydown={(e) => e.key === 'Escape' && cancelDelete()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="delete-modal-title"
    tabindex="0"
  >
    <div
      class="bg-white rounded-xl shadow-large max-w-md w-full"
      on:click|stopPropagation
      on:keydown|stopPropagation
      role="document"
    >
      <div class="px-6 py-4 border-b border-slate-200">
        <div class="flex items-center justify-between">
          <h3 id="delete-modal-title" class="text-lg font-semibold text-slate-900">Delete User</h3>
          <button
            class="text-slate-400 hover:text-slate-600 transition-colors"
            on:click={cancelDelete}
            aria-label="Close delete confirmation dialog"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <div class="px-6 py-4">
        <div class="flex items-center gap-3 mb-4">
          <div class="flex-shrink-0">
            <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-medium text-slate-900">Permanent Deletion</h4>
            <p class="text-slate-600">This action cannot be undone.</p>
          </div>
        </div>
        <p class="text-slate-600">
          Are you sure you want to permanently delete user <strong>{userToDelete.email}</strong>?
          All their data including tasks, categories, and settings will be permanently removed.
        </p>
      </div>
      <div class="px-6 py-4 border-t border-slate-200 flex gap-3 justify-end">
        <button
          class="btn btn-secondary"
          on:click={cancelDelete}
          disabled={deletingUser}
        >
          Cancel
        </button>
        <button
          class="btn btn-delete"
          on:click={deleteUser}
          disabled={deletingUser}
        >
          {#if deletingUser}
            Deleting...
          {:else}
            Delete User
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .admin-header {
    margin-bottom: 2rem;
  }

  .admin-header h1 {
    color: #1a202c;
    margin-bottom: 0.5rem;
  }

  .admin-subtitle {
    color: #718096;
    margin: 0;
  }

  .alert {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
  }

  .alert-error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }

  .alert-success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
  }

  .credentials-display {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .credentials-box {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
  }

  .credential-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .credential-item:last-child {
    margin-bottom: 0;
  }

  .credentials-box code {
    background: #e2e8f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: monospace;
  }

  .copy-btn {
    background: #4299e1;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
  }

  .copy-btn:hover {
    background: #3182ce;
    transform: scale(1.05);
  }

  .copy-btn:active {
    transform: scale(0.95);
  }

  .admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
  }

  .stat-card h3 {
    color: #4a5568;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
  }

  .stat-number {
    color: #1a202c;
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
  }

  .admin-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .create-user-form {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    display: block;
    color: #4a5568;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
  }

  .form-group input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #4299e1;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #3182ce;
  }

  .btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
  }

  .btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
  }

  .btn-reset {
    background: #f56565;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .btn-reset:hover:not(:disabled) {
    background: #e53e3e;
  }

  .users-table {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .users-table h3 {
    padding: 1.5rem;
    margin: 0;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .table-container {
    overflow-x: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
  }

  .status-badge, .role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .status-badge {
    background: #fed7d7;
    color: #c53030;
  }

  .status-badge.verified {
    background: #c6f6d5;
    color: #22543d;
  }

  .role-badge {
    background: #e2e8f0;
    color: #4a5568;
  }

  .role-badge.admin {
    background: #fbb6ce;
    color: #97266d;
  }

  .account-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    background: #c6f6d5;
    color: #22543d;
  }

  .account-badge.suspended {
    background: #fed7d7;
    color: #c53030;
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .btn-suspend {
    background: #f56565;
    color: white;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .btn-suspend:hover:not(:disabled) {
    background: #e53e3e;
  }

  .btn-unsuspend {
    background: #48bb78;
    color: white;
  }

  .btn-unsuspend:hover:not(:disabled) {
    background: #38a169;
  }

  .btn-delete {
    background: #e53e3e;
    color: white;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .btn-delete:hover:not(:disabled) {
    background: #c53030;
  }

  .btn-reset {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  @media (max-width: 768px) {
    .action-buttons {
      flex-direction: column;
      gap: 0.25rem;
    }

    .btn {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
    }
  }

  .loading, .empty-state {
    padding: 2rem;
    text-align: center;
    color: #718096;
  }

  @media (max-width: 768px) {
    .admin-container {
      padding: 1rem;
    }

    .admin-actions {
      flex-direction: column;
    }

    .form-actions {
      flex-direction: column;
    }

    .table-container {
      font-size: 0.875rem;
    }

    th, td {
      padding: 0.75rem 0.5rem;
    }
  }

  /* Modal Styles */
  .shadow-large {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Button Styles for Modals */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-white text-slate-700 border border-slate-300 hover:bg-slate-50 focus:ring-blue-500;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-suspend {
    @apply bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500;
  }

  .btn-delete {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  /* Search and Test Email Styles */
  .users-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .users-table-header h3 {
    margin: 0;
    color: #2d3748;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    width: 300px;
    transition: border-color 0.2s ease;
  }

  .search-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .clear-search-btn {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
  }

  .clear-search-btn:hover {
    color: #6b7280;
  }

  .btn-test-email {
    background: #10b981;
    color: white;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .btn-scheduler {
    background: #805ad5;
    color: white;
    display: flex;
    align-items: center;
  }

  .btn-scheduler:hover {
    background: #6b46c1;
  }

  .btn-test-email:hover:not(:disabled) {
    background: #059669;
  }

  .test-email-result {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .test-email-result.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  .test-email-result.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  @media (max-width: 768px) {
    .users-table-header {
      flex-direction: column;
      align-items: stretch;
    }

    .search-input {
      width: 100%;
    }

    .action-buttons {
      flex-direction: column;
      gap: 0.25rem;
    }

    .btn {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
    }
  }
</style>
