import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { taskScheduler } from '$lib/server/scheduler.js';
import { getSchedulerConfig } from '$lib/server/config/scheduler.js';
import { verifyAdminToken } from '$lib/server/auth.js';

export const GET: RequestHandler = async ({ request }) => {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult.success) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const status = taskScheduler.getStatus();
    const cronJobDetails = taskScheduler.getCronJobDetails();
    const config = getSchedulerConfig();

    return json({
      success: true,
      data: {
        scheduler: {
          isRunning: status.isRunning,
          cronJobCount: status.cronJobCount,
          cronJobs: status.cronJobs
        },
        cronJobDetails,
        configuration: {
          maintenanceCronExpression: config.maintenanceCronExpression,
          emailReminderCronExpression: config.emailReminderCronExpression,
          cronTimezone: config.cronTimezone,
          initialDelaySeconds: config.initialDelaySeconds,
          defaultReminderDays: config.defaultReminderDays,
          recurringGenerationYears: config.recurringGenerationYears,
          recurringMaintenanceMonths: config.recurringMaintenanceMonths
        }
      }
    });
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    return json(
      { error: 'Failed to get scheduler status' },
      { status: 500 }
    );
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult.success) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action } = await request.json();

    switch (action) {
      case 'start':
        taskScheduler.start();
        return json({ success: true, message: 'Scheduler started' });

      case 'stop':
        taskScheduler.stop();
        return json({ success: true, message: 'Scheduler stopped' });

      case 'restart':
        taskScheduler.stop();
        // Wait a moment before restarting
        setTimeout(() => {
          taskScheduler.start();
        }, 1000);
        return json({ success: true, message: 'Scheduler restarted' });

      case 'runMaintenance':
        await taskScheduler.runMaintenance();
        return json({ success: true, message: 'Manual maintenance completed' });

      case 'runEmailReminders':
        await taskScheduler.runEmailReminders();
        return json({ success: true, message: 'Manual email reminders completed' });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error controlling scheduler:', error);
    return json(
      { error: 'Failed to control scheduler' },
      { status: 500 }
    );
  }
};
